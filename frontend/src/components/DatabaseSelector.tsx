import React, { useState, useEffect } from 'react';
import { DatabaseInfo } from '../types';

interface DatabaseSelectorProps {
  selectedDatabaseId: string;
  selectedDatabase: string;
  selectedTable: string;
  autoDetectEnabled: boolean;
  onDatabaseChange: (databaseId: string, database: string, table: string, databaseType: string) => void;
  onAutoDetectChange: (enabled: boolean) => void;
}

interface DatabasesResponse {
  databases: DatabaseInfo[];
}

interface TablesResponse {
  databaseId: string;
  database: string;
  databaseType: string;
  tables: string[];
  collections: string[];
}

const DatabaseSelector: React.FC<DatabaseSelectorProps> = ({
  selectedDatabaseId,
  selectedDatabase,
  selectedTable,
  autoDetectEnabled,
  onDatabaseChange,
  onAutoDetectChange,
}) => {
  const [availableDatabases, setAvailableDatabases] = useState<DatabaseInfo[]>([]);
  const [availableDatabaseNames, setAvailableDatabaseNames] = useState<string[]>([]);
  const [availableTables, setAvailableTables] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available database configurations
  useEffect(() => {
    const fetchDatabases = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/databases');
        if (!response.ok) {
          throw new Error('Failed to fetch databases');
        }
        const data: DatabasesResponse = await response.json();
        setAvailableDatabases(data.databases);

        // Set default database names for the selected database config
        const selectedDbConfig = data.databases.find(db => db.id === selectedDatabaseId);
        if (selectedDbConfig) {
          setAvailableDatabaseNames(selectedDbConfig.databases || []);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch databases');
      } finally {
        setLoading(false);
      }
    };

    fetchDatabases();
  }, [selectedDatabaseId]);

  // Fetch available tables when database selection changes
  useEffect(() => {
    const fetchTables = async () => {
      if (!selectedDatabaseId || !selectedDatabase) return;

      try {
        setLoading(true);
        const response = await fetch(
          `/api/tables?databaseId=${encodeURIComponent(selectedDatabaseId)}&database=${encodeURIComponent(selectedDatabase)}`
        );
        if (!response.ok) {
          throw new Error('Failed to fetch tables');
        }
        const data: TablesResponse = await response.json();
        setAvailableTables(data.tables);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tables');
        setAvailableTables([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTables();
  }, [selectedDatabaseId, selectedDatabase]);

  const handleDatabaseConfigChange = (databaseId: string) => {
    const dbConfig = availableDatabases.find(db => db.id === databaseId);
    if (dbConfig) {
      setAvailableDatabaseNames(dbConfig.databases || []);
      const firstDatabase = dbConfig.databases?.[0] || 'test';
      onDatabaseChange(databaseId, firstDatabase, '', dbConfig.type);
    }
  };

  const handleDatabaseNameChange = (databaseName: string) => {
    const dbConfig = availableDatabases.find(db => db.id === selectedDatabaseId);
    if (dbConfig) {
      onDatabaseChange(selectedDatabaseId, databaseName, '', dbConfig.type);
    }
  };

  const handleTableChange = (tableName: string) => {
    const dbConfig = availableDatabases.find(db => db.id === selectedDatabaseId);
    if (dbConfig) {
      onDatabaseChange(selectedDatabaseId, selectedDatabase, tableName, dbConfig.type);
    }
  };

  if (loading && availableDatabases.length === 0) {
    return <div className="database-selector loading">Loading databases...</div>;
  }

  if (error) {
    return <div className="database-selector error">Error: {error}</div>;
  }

  const selectedDbConfig = availableDatabases.find(db => db.id === selectedDatabaseId);
  const entityName = selectedDbConfig?.type === 'mongodb' ? 'Collection' : 'Table';

  return (
    <div className="database-selector">
      <div className="auto-detect-section">
        <label className="auto-detect-toggle">
          <input
            type="checkbox"
            checked={autoDetectEnabled}
            onChange={(e) => onAutoDetectChange(e.target.checked)}
          />
          <span className="toggle-text">
            Auto-detect database and collection from your question
          </span>
        </label>
        {autoDetectEnabled && (
          <div className="auto-detect-info">
            💡 When enabled, AI will automatically identify the best database and collection based on your question.
          </div>
        )}
      </div>

      <div className="selector-row">
        <div className="selector-group">
          <label htmlFor="database-config">Database Type:</label>
          <select
            id="database-config"
            value={selectedDatabaseId}
            onChange={(e) => handleDatabaseConfigChange(e.target.value)}
            disabled={loading || autoDetectEnabled}
          >
            <option value="">Auto-detect...</option>
            {availableDatabases.map((db) => (
              <option key={db.id} value={db.id}>
                {db.name} ({db.type.toUpperCase()})
              </option>
            ))}
          </select>
        </div>

        <div className="selector-group">
          <label htmlFor="database-name">Database:</label>
          <select
            id="database-name"
            value={selectedDatabase}
            onChange={(e) => handleDatabaseNameChange(e.target.value)}
            disabled={loading || availableDatabaseNames.length === 0 || autoDetectEnabled}
          >
            <option value="">Auto-detect...</option>
            {availableDatabaseNames.map((dbName) => (
              <option key={dbName} value={dbName}>
                {dbName}
              </option>
            ))}
          </select>
        </div>

        <div className="selector-group">
          <label htmlFor="table-name">{entityName}:</label>
          <select
            id="table-name"
            value={selectedTable}
            onChange={(e) => handleTableChange(e.target.value)}
            disabled={loading || availableTables.length === 0 || autoDetectEnabled}
          >
            <option value="">Auto-detect...</option>
            {availableTables.map((tableName) => (
              <option key={tableName} value={tableName}>
                {tableName}
              </option>
            ))}
          </select>
        </div>
      </div>

      {selectedDbConfig && (
        <div className="database-info">
          <span className="database-type-badge" data-type={selectedDbConfig.type}>
            {selectedDbConfig.type.toUpperCase()}
          </span>
          {selectedDatabase && selectedTable && (
            <span className="selected-target">
              {selectedDatabase}.{selectedTable}
            </span>
          )}
        </div>
      )}

      <style>{`
        .database-selector {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;
        }

        .auto-detect-section {
          margin-bottom: 16px;
          padding-bottom: 16px;
          border-bottom: 1px solid #e9ecef;
        }

        .auto-detect-toggle {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          font-weight: 500;
          color: #495057;
        }

        .auto-detect-toggle input[type="checkbox"] {
          width: 16px;
          height: 16px;
          cursor: pointer;
        }

        .toggle-text {
          user-select: none;
        }

        .auto-detect-info {
          margin-top: 8px;
          padding: 8px 12px;
          background: #e7f3ff;
          border: 1px solid #b3d9ff;
          border-radius: 4px;
          font-size: 12px;
          color: #0066cc;
        }

        .selector-row {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: end;
        }

        .selector-group {
          display: flex;
          flex-direction: column;
          min-width: 150px;
          flex: 1;
        }

        .selector-group label {
          font-size: 12px;
          font-weight: 600;
          color: #495057;
          margin-bottom: 4px;
        }

        .selector-group select {
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          background: white;
          font-size: 14px;
          color: #495057;
        }

        .selector-group select:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .selector-group select:disabled {
          background: #e9ecef;
          color: #6c757d;
          cursor: not-allowed;
        }

        .database-info {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #e9ecef;
        }

        .database-type-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
        }

        .database-type-badge[data-type="mongodb"] {
          background: #4caf50;
          color: white;
        }

        .database-type-badge[data-type="postgresql"] {
          background: #336791;
          color: white;
        }

        .database-type-badge[data-type="mysql"] {
          background: #f29111;
          color: white;
        }

        .selected-target {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          color: #495057;
          background: #e9ecef;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .loading, .error {
          padding: 16px;
          text-align: center;
          border-radius: 8px;
        }

        .loading {
          background: #e3f2fd;
          color: #1976d2;
        }

        .error {
          background: #ffebee;
          color: #c62828;
        }

        @media (max-width: 768px) {
          .selector-row {
            flex-direction: column;
          }

          .selector-group {
            min-width: unset;
          }
        }
      `}</style>
    </div>
  );
};

export default DatabaseSelector;
