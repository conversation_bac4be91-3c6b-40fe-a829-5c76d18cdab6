import { ChatOllama } from '@langchain/community/chat_models/ollama';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { DatabaseService, DatabaseSchema, QueryResult } from '../types/database';
import { ConfigManager } from './ConfigManager';

export class DatabaseLangChainService {
  private llm: ChatOllama;
  private databaseService: DatabaseService;
  private configManager: ConfigManager;

  constructor(databaseService: DatabaseService, modelName: string = 'llama2') {
    this.databaseService = databaseService;
    this.configManager = ConfigManager.getInstance();
    this.llm = new ChatOllama({
      baseUrl: 'http://localhost:11434',
      model: modelName,
      temperature: 0.1,
    });
  }

  private createQueryGenerationPrompt(databaseType: string): PromptTemplate {
    const promptTemplate = this.configManager.getPromptForDatabase(databaseType, 'queryGeneration');
    if (!promptTemplate) {
      throw new Error(`No query generation prompt found for database type: ${databaseType}`);
    }
    return PromptTemplate.fromTemplate(promptTemplate);
  }

  private createResponseGenerationPrompt(databaseType: string): PromptTemplate {
    const promptTemplate = this.configManager.getPromptForDatabase(databaseType, 'responseGeneration');
    if (!promptTemplate) {
      throw new Error(`No response generation prompt found for database type: ${databaseType}`);
    }
    return PromptTemplate.fromTemplate(promptTemplate);
  }

  private createIntentClassificationPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(`
Analyze the following question and determine if it's asking about database/data or if it's a general conversation.

Question: "{question}"
Available Fields: {fields}

Database-related questions include:
- Counting records ("How many users?", "Count of orders")
- Querying data ("Show me users", "Find active customers")
- Data analysis ("Average age", "Total sales")
- Filtering/searching ("Users from New York", "Orders this month")
- Data statistics ("Most popular product", "Latest entries")

General conversation includes:
- Greetings ("Hello", "Hi there")
- General questions ("How are you?", "What can you do?")
- Non-data topics ("Tell me a joke", "What's the weather?")
- System questions not about data ("What is this app?", "How does this work?")

Respond with ONLY one word:
- "DATABASE" if the question is asking about data/database
- "GENERAL" if the question is general conversation

Classification:
`);
  }

  private createGeneralConversationPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(`
You are a helpful database assistant. The user asked a general question that's not related to querying data.

User Question: "{question}"

Respond in a friendly, helpful way. Let them know that you specialize in helping with database queries and data analysis. If appropriate, suggest how they might ask database-related questions.

Keep your response conversational and helpful.

Response:
`);
  }

  private async classifyIntent(
    question: string,
    fields: string[]
  ): Promise<'DATABASE' | 'GENERAL'> {
    try {
      const intentPrompt = this.createIntentClassificationPrompt();
      const intentChain = RunnableSequence.from([
        intentPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      console.log(`Classifying intent for: "${question}"`);
      const intentResult = await Promise.race([
        intentChain.invoke({
          question,
          fields: fields.join(', '),
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Intent classification timed out')), 30000)
        )
      ]) as string;

      const classification = intentResult.trim().toUpperCase();
      console.log(`Intent classified as: ${classification}`);

      // Default to DATABASE if classification is unclear
      return classification.includes('DATABASE') ? 'DATABASE' :
             classification.includes('GENERAL') ? 'GENERAL' : 'DATABASE';
    } catch (error) {
      console.error('Error classifying intent:', error);
      // Default to DATABASE on error to maintain existing functionality
      return 'DATABASE';
    }
  }

  private async handleGeneralConversation(
    question: string
  ): Promise<{ response: string; query?: any; results?: any[] }> {
    try {
      const conversationPrompt = this.createGeneralConversationPrompt();
      const conversationChain = RunnableSequence.from([
        conversationPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      console.log('Handling general conversation...');
      const response = await Promise.race([
        conversationChain.invoke({ question }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('General conversation timed out')), 30000)
        )
      ]) as string;

      return {
        response: response.trim(),
        query: undefined,
        results: undefined,
      };
    } catch (error) {
      console.error('Error handling general conversation:', error);
      return {
        response: "Hello! I'm a database assistant that helps you query and analyze your data. You can ask me questions like 'How many users do we have?' or 'Show me all active customers'. What would you like to know about your data?",
        query: undefined,
        results: undefined,
      };
    }
  }

  async processUserQuestion(
    question: string,
    tableName: string,
    databaseName?: string
  ): Promise<{ response: string; query?: any; results?: any[] }> {
    try {
      const databaseType = this.databaseService.getType();

      // Connect to database and get schema
      await this.databaseService.connect(databaseName);
      const schema = await this.databaseService.getTableSchema(tableName, databaseName);

      // First, classify the intent of the question
      const intent = await this.classifyIntent(question, schema.fields || []);

      // If it's general conversation, handle it without database operations
      if (intent === 'GENERAL') {
        return await this.handleGeneralConversation(question);
      }

      // Check if table/collection exists and has data
      if (!schema.exists) {
        const entityName = databaseType === 'mongodb' ? 'collection' : 'table';
        const dbName = databaseName || 'default database';
        return {
          response: `The ${entityName} '${tableName}' doesn't exist in the '${dbName}' database. You might want to create some data first or check if you're using the correct ${entityName} name.`,
          query: undefined,
          results: undefined,
        };
      }

      const recordCount = schema.documentCount || schema.rowCount || 0;
      if (recordCount === 0) {
        const entityName = databaseType === 'mongodb' ? 'collection' : 'table';
        return {
          response: `The ${entityName} '${tableName}' exists but is empty. You might want to add some data first before querying.`,
          query: undefined,
          results: undefined,
        };
      }

      // Create schema description for the prompt
      const schemaDescription = this.formatSchemaForPrompt(schema, databaseType);

      // Generate query using LangChain
      const queryPrompt = this.createQueryGenerationPrompt(databaseType);
      const queryChain = RunnableSequence.from([
        queryPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      console.log('Generating query for question:', question);
      const queryResponse = await Promise.race([
        queryChain.invoke({
          schema: schemaDescription,
          question: question,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Query generation timed out')), 60000)
        )
      ]) as string;

      console.log('Generated query response:', queryResponse);

      // Parse the generated query
      let parsedQuery: string | object;
      try {
        if (databaseType === 'mongodb') {
          // Clean the response to extract JSON for MongoDB
          const cleanedResponse = this.cleanQueryResponse(queryResponse);
          parsedQuery = JSON.parse(cleanedResponse);
        } else {
          // For SQL databases, extract the SQL query
          parsedQuery = this.extractSQLQuery(queryResponse);
        }
      } catch (parseError) {
        console.error('Failed to parse generated query:', parseError);
        return {
          response: 'I had trouble understanding your question. Could you please rephrase it or be more specific?',
          query: queryResponse,
          results: undefined,
        };
      }

      // Execute the query
      console.log('Executing parsed query:', typeof parsedQuery === 'object' ? JSON.stringify(parsedQuery, null, 2) : parsedQuery);
      const queryResult = await this.databaseService.executeQuery(parsedQuery, { collection: tableName, table: tableName });

      // Generate natural language response
      const responsePrompt = this.createResponseGenerationPrompt(databaseType);
      const responseChain = RunnableSequence.from([
        responsePrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      const naturalResponse = await Promise.race([
        responseChain.invoke({
          question: question,
          query: typeof parsedQuery === 'object' ? JSON.stringify(parsedQuery, null, 2) : parsedQuery,
          results: JSON.stringify(queryResult.results.slice(0, 3), null, 2), // Show first 3 results for context
          count: queryResult.count,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Response generation timed out')), 60000)
        )
      ]) as string;

      return {
        response: naturalResponse,
        query: parsedQuery,
        results: queryResult.results,
      };
    } catch (error) {
      console.error('Error processing user question:', error);
      return {
        response: 'I encountered an error while processing your question. Please try again or rephrase your question.',
        query: undefined,
        results: undefined,
      };
    }
  }

  private formatSchemaForPrompt(schema: DatabaseSchema, databaseType: string): string {
    let description = '';

    if (databaseType === 'mongodb') {
      description = `Collection: ${schema.collection}\n`;
      description += `Document Count: ${schema.documentCount}\n`;
    } else {
      description = `Table: ${schema.table}\n`;
      description += `Schema: ${schema.schema}\n`;
      description += `Row Count: ${schema.rowCount}\n`;
    }

    if (schema.fields && schema.fields.length > 0) {
      const fieldLabel = databaseType === 'mongodb' ? 'Available Fields' : 'Available Columns';
      description += `\n${fieldLabel}:\n`;
      schema.fields.forEach((field: string) => {
        description += `- ${field}\n`;
      });
    }

    if (schema.sampleDocument) {
      const sampleLabel = databaseType === 'mongodb' ? 'Sample Document Structure' : 'Sample Row Structure';
      description += `\n${sampleLabel}:\n`;
      description += JSON.stringify(schema.sampleDocument, null, 2);
    }

    return description;
  }

  private cleanQueryResponse(response: string): string {
    // Remove any markdown formatting
    let cleaned = response.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Remove any explanatory text before or after the JSON
    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleaned = jsonMatch[0];
    }

    // Remove any trailing text after the JSON
    const lines = cleaned.split('\n');
    let jsonLines = [];
    let braceCount = 0;
    let foundStart = false;

    for (const line of lines) {
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          foundStart = true;
        } else if (char === '}') {
          braceCount--;
        }
      }

      if (foundStart) {
        jsonLines.push(line);
      }

      if (foundStart && braceCount === 0) {
        break;
      }
    }

    return jsonLines.join('\n').trim();
  }

  private extractSQLQuery(response: string): string {
    // Remove any markdown formatting
    let cleaned = response.replace(/```sql\s*/g, '').replace(/```\s*/g, '');

    // Look for SQL query patterns
    const sqlMatch = cleaned.match(/(SELECT[\s\S]*?;?)|(INSERT[\s\S]*?;?)|(UPDATE[\s\S]*?;?)|(DELETE[\s\S]*?;?)/i);
    if (sqlMatch) {
      cleaned = sqlMatch[0];
    }

    // Clean up the query
    cleaned = cleaned.trim();
    if (!cleaned.endsWith(';')) {
      cleaned += ';';
    }

    return cleaned;
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.llm.invoke('Hello, are you working?');
      console.log('Ollama connection test successful');
      return true;
    } catch (error) {
      console.error('Ollama connection test failed:', error);
      return false;
    }
  }
}
