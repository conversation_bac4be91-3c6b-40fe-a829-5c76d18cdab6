import { Router, Request, Response } from 'express';
import { DatabaseLangChainService } from '../services/DatabaseLangChainService';
import { DatabaseManager } from '../services/DatabaseManager';
import { DatabaseDetectionService } from '../services/DatabaseDetectionService';
import { ConfigManager } from '../services/ConfigManager';
import { ChatRequest, ChatResponse, DatabaseInfo } from '../types';

const router = Router();

// Initialize services
const databaseManager = DatabaseManager.getInstance();
const configManager = ConfigManager.getInstance();
const detectionService = new DatabaseDetectionService(databaseManager, process.env.OLLAMA_MODEL || 'llama2');
console.log('Database system initialized with Ollama model:', process.env.OLLAMA_MODEL);

router.post('/chat', async (req: Request, res: Response) => {
  try {
    const {
      message,
      database,
      collection,
      table,
      databaseId,
      databaseType,
      autoDetect = true
    }: ChatRequest & { autoDetect?: boolean } = req.body;

    if (!message) {
      return res.status(400).json({
        error: 'Message is required'
      } as ChatResponse);
    }

    let finalDatabaseId = databaseId;
    let finalDatabase = database;
    let finalTable = table;
    let finalCollection = collection;
    let detectionInfo: string | undefined;

    // If any required parameters are missing and autoDetect is enabled, use AI detection
    if (autoDetect && (!databaseId || !database)) {
      console.log('Running AI detection for missing database parameters...');

      try {
        const detection = await detectionService.detectDatabaseAndTable(message);

        // Use detected values if confidence is reasonable (>= 0.5) or if no values were provided
        if (detection.confidence >= 0.5 || (!databaseId && !database)) {
          finalDatabaseId = detection.databaseId || finalDatabaseId;
          finalDatabase = detection.databaseName || finalDatabase;

          detectionInfo = `AI detected database: ${detection.reasoning} (confidence: ${(detection.confidence * 100).toFixed(1)}%)`;
          console.log('AI Detection result:', detection);
        } else {
          console.log('AI detection confidence too low, using defaults');
          detectionInfo = `AI detection confidence too low (${(detection.confidence * 100).toFixed(1)}%), using defaults`;
        }
      } catch (detectionError) {
        console.warn('AI detection failed, using defaults:', detectionError);
        detectionInfo = 'AI detection failed, using defaults';
      }
    }

    // Fall back to defaults if still missing database info
    if (!finalDatabaseId || !finalDatabase) {
      const defaults = detectionService.getDefaultValues();
      finalDatabaseId = finalDatabaseId || defaults.databaseId;
      finalDatabase = finalDatabase || defaults.databaseName;

      if (!detectionInfo) {
        detectionInfo = 'Using default database';
      }
    }

    // Get the database service
    const databaseService = databaseManager.getDatabaseService(finalDatabaseId);
    const actualDatabaseType = databaseService.getType();

    // Create LangChain service for this database
    const langchainService = new DatabaseLangChainService(
      databaseService,
      process.env.OLLAMA_MODEL || 'llama2'
    );

    // Collection/table will be auto-detected by the LangChain service
    console.log(`Processing question: "${message}" for ${finalDatabaseId} (${actualDatabaseType}) - ${finalDatabase}`);
    if (detectionInfo) {
      console.log(`Detection info: ${detectionInfo}`);
    }

    // Process the user's question (collection will be auto-detected)
    const result = await langchainService.processUserQuestion(message, undefined, finalDatabase);

    // Add collection detection info to the detection message
    let finalDetectionInfo = detectionInfo;
    if (result.detectedCollection) {
      const collectionInfo = `Collection auto-detected: ${result.detectedCollection}`;
      finalDetectionInfo = detectionInfo ? `${detectionInfo}. ${collectionInfo}` : collectionInfo;
    }

    const response: ChatResponse = {
      response: result.response,
      query: result.query,
      results: result.results,
      databaseType: actualDatabaseType,
      detectionInfo: finalDetectionInfo,
      usedDatabase: finalDatabase,
      usedTable: result.detectedCollection || 'auto-detected',
      usedDatabaseId: finalDatabaseId
    };

    res.json(response);

  } catch (error) {
    console.error('Chat endpoint error:', error);

    const errorResponse: ChatResponse = {
      response: 'I apologize, but I encountered an error while processing your request. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };

    res.status(500).json(errorResponse);
  }
});

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  try {
    // Test Ollama connection
    const enabledDatabases = databaseManager.getEnabledDatabases();
    let ollamaStatus = false;

    if (enabledDatabases.length > 0) {
      try {
        const firstDbService = databaseManager.getDatabaseService(enabledDatabases[0].id);
        const langchainService = new DatabaseLangChainService(
          firstDbService,
          process.env.OLLAMA_MODEL || 'llama2'
        );
        ollamaStatus = await langchainService.testConnection();
      } catch (error) {
        console.error('Ollama health check failed:', error);
      }
    }

    // Test all database connections
    const databaseStatuses = await databaseManager.getAllDatabasesStatus();

    res.json({
      status: 'ok',
      services: {
        ollama: ollamaStatus ? 'connected' : 'disconnected',
        databases: databaseStatuses
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get available databases
router.get('/databases', async (req: Request, res: Response) => {
  try {
    const enabledDatabases = databaseManager.getEnabledDatabases();
    const databasesInfo: DatabaseInfo[] = [];

    for (const config of enabledDatabases) {
      try {
        const service = databaseManager.getDatabaseService(config.id);
        const isConnected = await service.testConnection();

        if (isConnected) {
          await service.connect();
          const databases = await service.listDatabases();

          databasesInfo.push({
            id: config.id,
            name: config.name,
            type: config.type,
            enabled: config.enabled,
            databases
          });
        } else {
          databasesInfo.push({
            id: config.id,
            name: config.name,
            type: config.type,
            enabled: config.enabled,
            databases: []
          });
        }
      } catch (error) {
        console.error(`Error getting info for database ${config.id}:`, error);
        databasesInfo.push({
          id: config.id,
          name: config.name,
          type: config.type,
          enabled: config.enabled,
          databases: []
        });
      }
    }

    res.json({
      databases: databasesInfo
    });
  } catch (error) {
    console.error('Databases endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch databases'
    });
  }
});

// Get available tables/collections for a specific database
router.get('/tables', async (req: Request, res: Response) => {
  try {
    const databaseId = req.query.databaseId as string;
    const database = req.query.database as string;

    if (!databaseId) {
      return res.status(400).json({
        error: 'databaseId parameter is required'
      });
    }

    const service = databaseManager.getDatabaseService(databaseId);
    await service.connect(database);
    const tables = await service.listTables(database);
    const databaseType = service.getType();

    res.json({
      databaseId,
      database: database || 'default',
      databaseType,
      tables,
      // For backward compatibility
      collections: databaseType === 'mongodb' ? tables : []
    });
  } catch (error) {
    console.error('Tables endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch tables'
    });
  }
});

// Get available collections (backward compatibility)
router.get('/collections', async (req: Request, res: Response) => {
  try {
    const database = req.query.database as string || 'test';
    const databaseId = req.query.databaseId as string || 'mongodb';

    const service = databaseManager.getDatabaseService(databaseId);
    await service.connect(database);
    const collections = await service.listTables(database);

    res.json({
      database,
      collections
    });
  } catch (error) {
    console.error('Collections endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch collections'
    });
  }
});

export default router;
