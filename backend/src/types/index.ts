export interface ChatMessage {
  id: string;
  message: string;
  timestamp: Date;
  isUser: boolean;
}

export interface ChatRequest {
  message: string;
  database?: string;
  collection?: string;
  table?: string;
  databaseId?: string; // ID of the database configuration to use
  databaseType?: string; // Type of database (mongodb, postgresql, mysql)
}

export interface ChatResponse {
  response: string;
  query?: string | object;
  results?: any[];
  error?: string;
  executionTime?: number;
  databaseType?: string;
}

export interface DatabaseSchema {
  database: string;
  collection?: string; // For MongoDB
  table?: string; // For SQL databases
  schema?: string; // For SQL databases
  sampleDocument?: any;
  fields?: string[];
  indexes?: any[];
  exists?: boolean;
  documentCount?: number; // For MongoDB
  rowCount?: number; // For SQL databases
}

export interface DatabaseInfo {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  databases?: string[];
  tables?: string[];
  collections?: string[];
}
